2025-06-12 00:01:54 - APIServer - INFO - API服务器已停止
2025-06-12 00:01:54 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 00:01:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:01:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:01:59 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:01:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:01:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:01:59 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:02:11 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:02:11 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:02:11 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:02:11 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:02:11 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:02:11 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:04:11 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:04:11 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:04:11 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:04:11 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:04:11 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:04:11 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:04:14 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:04:14 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:04:14 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:04:14 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:04:14 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:04:14 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:04:16 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-12 00:04:16 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 3}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': '2025-06-10T20:19:25'}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-12 00:04:16 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-12 00:04:16 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:16 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:16 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-12 00:04:16 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:16 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-12 00:04:16 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-12 00:04:16 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-12 00:04:16 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-12 00:04:16 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-12 00:04:16 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 3}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': '2025-06-10T20:19:25'}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:16 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-12 00:04:16 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:16 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:16 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-12 00:04:16 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:16 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-12 00:04:16 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-12 00:04:16 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-12 00:04:16 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-12 00:04:17 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-12 00:04:17 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-12 00:04:17 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 3}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': '2025-06-10T20:19:25'}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-12 00:04:17 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-12 00:04:17 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:17 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:17 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-12 00:04:17 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:17 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: large
2025-06-12 00:04:17 - APIServer - INFO - 缩略图路径: data\thumbnails\large\ebfc0a072451aba9096854a34a4c1106_large.jpg
2025-06-12 00:04:17 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\ebfc0a072451aba9096854a34a4c1106_large.jpg
2025-06-12 00:04:17 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\ebfc0a072451aba9096854a34a4c1106_large.jpg
2025-06-12 00:04:17 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 3}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': '2025-06-10T20:19:25'}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-12 00:04:17 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-12 00:04:17 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:17 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:17 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-12 00:04:17 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:17 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: large
2025-06-12 00:04:17 - APIServer - INFO - 缩略图路径: data\thumbnails\large\ebfc0a072451aba9096854a34a4c1106_large.jpg
2025-06-12 00:04:17 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\ebfc0a072451aba9096854a34a4c1106_large.jpg
2025-06-12 00:04:17 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\ebfc0a072451aba9096854a34a4c1106_large.jpg
2025-06-12 00:04:19 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:04:19 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:04:19 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:04:19 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:04:19 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:04:19 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 9
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 9, 文件信息: {'id': 9, 'folder_id': 4, 'filename': '3.jpg', 'relative_path': '3.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': '.jpg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T18:26:01', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 11
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 11, 文件信息: {'id': 11, 'folder_id': 4, 'filename': '3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 3}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 12
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 12, 文件信息: {'id': 12, 'folder_id': 4, 'filename': '3 - 副本 (10) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (10) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (10) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10) - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10) - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\d9c2f08712ad4744eef5aac8bb0b3f12_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d9c2f08712ad4744eef5aac8bb0b3f12_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d9c2f08712ad4744eef5aac8bb0b3f12_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 13
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 13, 文件信息: {'id': 13, 'folder_id': 4, 'filename': '3 - 副本 (10) - 副本.jpg', 'relative_path': '3 - 副本 (10) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 3}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (10) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10) - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10) - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\830d0b30fe5c4caeb0bbb6dbb60e984d_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\830d0b30fe5c4caeb0bbb6dbb60e984d_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\830d0b30fe5c4caeb0bbb6dbb60e984d_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 15
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 15, 文件信息: {'id': 15, 'folder_id': 4, 'filename': '3 - 副本 (11) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (11) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (11) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (11) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (11) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (11) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\8b922b93c487c2f6d0c87a79c4718dac_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8b922b93c487c2f6d0c87a79c4718dac_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8b922b93c487c2f6d0c87a79c4718dac_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 16
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 16, 文件信息: {'id': 16, 'folder_id': 4, 'filename': '3 - 副本 (11) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (11) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (11) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (11) - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (11) - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (11) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 14
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 14, 文件信息: {'id': 14, 'folder_id': 4, 'filename': '3 - 副本 (10).jpg', 'relative_path': '3 - 副本 (10).jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (10).jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10).jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10).jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10).jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 17
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 17, 文件信息: {'id': 17, 'folder_id': 4, 'filename': '3 - 副本 (11) - 副本.jpg', 'relative_path': '3 - 副本 (11) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (11) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (11) - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (11) - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (11) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\7b5de39498aacc4574a9b9168f3f2116_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7b5de39498aacc4574a9b9168f3f2116_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7b5de39498aacc4574a9b9168f3f2116_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 18
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 18, 文件信息: {'id': 18, 'folder_id': 4, 'filename': '3 - 副本 (11).jpg', 'relative_path': '3 - 副本 (11).jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (11).jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (11).jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (11).jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (11).jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\d88afa30afe6d2552b6f6400f4860e7e_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d88afa30afe6d2552b6f6400f4860e7e_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d88afa30afe6d2552b6f6400f4860e7e_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 19
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 19, 文件信息: {'id': 19, 'folder_id': 4, 'filename': '3 - 副本 (12) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (12) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (12) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (12) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (12) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (12) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\0aff45f1acf1fb0c9eb59d16a6418372_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0aff45f1acf1fb0c9eb59d16a6418372_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0aff45f1acf1fb0c9eb59d16a6418372_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 20
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 20, 文件信息: {'id': 20, 'folder_id': 4, 'filename': '3 - 副本 (12) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (12) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (12) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (12) - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (12) - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (12) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\c6c26eeac0bad86fd939e6a7ce5a72ea_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c6c26eeac0bad86fd939e6a7ce5a72ea_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c6c26eeac0bad86fd939e6a7ce5a72ea_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 21
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 21, 文件信息: {'id': 21, 'folder_id': 4, 'filename': '3 - 副本 (12) - 副本.jpg', 'relative_path': '3 - 副本 (12) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (12) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (12) - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (12) - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (12) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\dd0d28c6962026b7fc4a923ec91bdc74_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\dd0d28c6962026b7fc4a923ec91bdc74_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\dd0d28c6962026b7fc4a923ec91bdc74_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 22
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 22, 文件信息: {'id': 22, 'folder_id': 4, 'filename': '3 - 副本 (13) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (13) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (13) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (13) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (13) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (13) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\0ed21dea8ec3000e0627be81ab39c9fa_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0ed21dea8ec3000e0627be81ab39c9fa_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0ed21dea8ec3000e0627be81ab39c9fa_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 23
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 23, 文件信息: {'id': 23, 'folder_id': 4, 'filename': '3 - 副本 (13) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (13) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (13) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (13) - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (13) - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (13) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\57756ed7d7c1f7026e0d45a00e5926b9_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\57756ed7d7c1f7026e0d45a00e5926b9_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\57756ed7d7c1f7026e0d45a00e5926b9_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 24
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 24, 文件信息: {'id': 24, 'folder_id': 4, 'filename': '3 - 副本 (13) - 副本.jpg', 'relative_path': '3 - 副本 (13) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (13) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (13) - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (13) - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (13) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\046b1e4fcd9f408e3824d628a0764123_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\046b1e4fcd9f408e3824d628a0764123_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\046b1e4fcd9f408e3824d628a0764123_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 25
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 25, 文件信息: {'id': 25, 'folder_id': 4, 'filename': '3 - 副本 (14) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (14) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (14) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (14) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (14) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (14) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\d2893cf50f58df4ef6879da35725da63_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d2893cf50f58df4ef6879da35725da63_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d2893cf50f58df4ef6879da35725da63_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 26
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 26, 文件信息: {'id': 26, 'folder_id': 4, 'filename': '3 - 副本 (14) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (14) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (14) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (14) - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (14) - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (14) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\2746bd5f20f8614a623b7f0be3178561_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2746bd5f20f8614a623b7f0be3178561_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2746bd5f20f8614a623b7f0be3178561_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 27
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 27, 文件信息: {'id': 27, 'folder_id': 4, 'filename': '3 - 副本 (14) - 副本.jpg', 'relative_path': '3 - 副本 (14) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (14) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (14) - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (14) - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (14) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\3cf78ed2eb5050e3263665a5ba1addb2_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3cf78ed2eb5050e3263665a5ba1addb2_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3cf78ed2eb5050e3263665a5ba1addb2_medium.jpg
2025-06-12 00:04:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 28
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件ID: 28, 文件信息: {'id': 28, 'folder_id': 4, 'filename': '3 - 副本 (15) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (15) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (15) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (15) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (15) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (15) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\2ba5f6f3f1c8725d3e7fc07ef7d02edb_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2ba5f6f3f1c8725d3e7fc07ef7d02edb_medium.jpg
2025-06-12 00:04:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2ba5f6f3f1c8725d3e7fc07ef7d02edb_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 29
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 29, 文件信息: {'id': 29, 'folder_id': 4, 'filename': '3 - 副本 (15) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (15) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (15) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (15) - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (15) - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (15) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\012a765d5cd6170a42f30117f9415784_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\012a765d5cd6170a42f30117f9415784_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\012a765d5cd6170a42f30117f9415784_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 30
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 30, 文件信息: {'id': 30, 'folder_id': 4, 'filename': '3 - 副本 (15) - 副本.jpg', 'relative_path': '3 - 副本 (15) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (15) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (15) - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (15) - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (15) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\16d56b05cec65b78790cf0f244a56c01_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\16d56b05cec65b78790cf0f244a56c01_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\16d56b05cec65b78790cf0f244a56c01_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 31
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 31, 文件信息: {'id': 31, 'folder_id': 4, 'filename': '3 - 副本 (16) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (16) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (16) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (16) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (16) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (16) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\f00d49302bea9d9b885d84df38c40ee4_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f00d49302bea9d9b885d84df38c40ee4_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f00d49302bea9d9b885d84df38c40ee4_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 32
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 32, 文件信息: {'id': 32, 'folder_id': 4, 'filename': '3 - 副本 (16) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (16) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (16) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (16) - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (16) - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (16) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 33
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 33, 文件信息: {'id': 33, 'folder_id': 4, 'filename': '3 - 副本 (16) - 副本.jpg', 'relative_path': '3 - 副本 (16) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (16) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (16) - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (16) - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (16) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\79f8b1ee1c99bcfd449bd1bf51d981cf_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\79f8b1ee1c99bcfd449bd1bf51d981cf_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\79f8b1ee1c99bcfd449bd1bf51d981cf_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 34
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 34, 文件信息: {'id': 34, 'folder_id': 4, 'filename': '3 - 副本 (17) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (17) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (17) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (17) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (17) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (17) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ed36689f714899e7f6825bc6c76584c3_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ed36689f714899e7f6825bc6c76584c3_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ed36689f714899e7f6825bc6c76584c3_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 35
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 35, 文件信息: {'id': 35, 'folder_id': 4, 'filename': '3 - 副本 (17) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (17) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (17) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (17) - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (17) - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (17) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\587ff386c900b5eb6feb7c47136a903e_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\587ff386c900b5eb6feb7c47136a903e_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\587ff386c900b5eb6feb7c47136a903e_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 36
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 36, 文件信息: {'id': 36, 'folder_id': 4, 'filename': '3 - 副本 (17) - 副本.jpg', 'relative_path': '3 - 副本 (17) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (17) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (17) - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (17) - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (17) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\68320b334d4b6ab9fba2f921f14d559f_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\68320b334d4b6ab9fba2f921f14d559f_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\68320b334d4b6ab9fba2f921f14d559f_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 37
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 37, 文件信息: {'id': 37, 'folder_id': 4, 'filename': '3 - 副本 (18) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (18) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (18) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (18) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (18) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (18) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\aeb3b8190bd9d511701e57070714ce45_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\aeb3b8190bd9d511701e57070714ce45_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\aeb3b8190bd9d511701e57070714ce45_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 38
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 38, 文件信息: {'id': 38, 'folder_id': 4, 'filename': '3 - 副本 (18) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (18) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (18) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (18) - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (18) - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (18) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\589e9302eb8a3ff655894f2ae492726c_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\589e9302eb8a3ff655894f2ae492726c_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\589e9302eb8a3ff655894f2ae492726c_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 39
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 39, 文件信息: {'id': 39, 'folder_id': 4, 'filename': '3 - 副本 (18) - 副本.jpg', 'relative_path': '3 - 副本 (18) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (18) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (18) - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (18) - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (18) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\4f85f562f0eeb9d4d77d479fde830750_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\4f85f562f0eeb9d4d77d479fde830750_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\4f85f562f0eeb9d4d77d479fde830750_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 40
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 40, 文件信息: {'id': 40, 'folder_id': 4, 'filename': '3 - 副本 (19) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (19) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (19) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (19) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (19) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (19) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\8f5e5b0657e5abf39b3cceced7d24d14_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8f5e5b0657e5abf39b3cceced7d24d14_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8f5e5b0657e5abf39b3cceced7d24d14_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 41
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 41, 文件信息: {'id': 41, 'folder_id': 4, 'filename': '3 - 副本 (19) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (19) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (19) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (19) - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (19) - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (19) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\fd7c070a2a5d88a2ff9410243d2ab01a_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\fd7c070a2a5d88a2ff9410243d2ab01a_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\fd7c070a2a5d88a2ff9410243d2ab01a_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 42
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 42, 文件信息: {'id': 42, 'folder_id': 4, 'filename': '3 - 副本 (19) - 副本.jpg', 'relative_path': '3 - 副本 (19) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (19) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (19) - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (19) - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (19) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\a14bafdec39e39159641f49061fd8aa5_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\a14bafdec39e39159641f49061fd8aa5_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\a14bafdec39e39159641f49061fd8aa5_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 43
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 43, 文件信息: {'id': 43, 'folder_id': 4, 'filename': '3 - 副本 (2) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (2) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (2) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (2) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (2) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (2) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\c7020ff92ba16ce195c616344c960ed6_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c7020ff92ba16ce195c616344c960ed6_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c7020ff92ba16ce195c616344c960ed6_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 44
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 44, 文件信息: {'id': 44, 'folder_id': 4, 'filename': '3 - 副本 (2) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (2) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (2) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (2) - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (2) - 副本 - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (2) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\aaa5464dc3f938d9b1c932b91e59f407_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\aaa5464dc3f938d9b1c932b91e59f407_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\aaa5464dc3f938d9b1c932b91e59f407_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 46
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 46, 文件信息: {'id': 46, 'folder_id': 4, 'filename': '3 - 副本 (2).jpg', 'relative_path': '3 - 副本 (2).jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (2).jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (2).jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (2).jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (2).jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\59b2961926dcfa3f564a72391d7e986c_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\59b2961926dcfa3f564a72391d7e986c_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\59b2961926dcfa3f564a72391d7e986c_medium.jpg
2025-06-12 00:04:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 45
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件ID: 45, 文件信息: {'id': 45, 'folder_id': 4, 'filename': '3 - 副本 (2) - 副本.jpg', 'relative_path': '3 - 副本 (2) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (2) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (2) - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (2) - 副本.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (2) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\40ca54c51a97504ccca9d5057d97e609_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\40ca54c51a97504ccca9d5057d97e609_medium.jpg
2025-06-12 00:04:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\40ca54c51a97504ccca9d5057d97e609_medium.jpg
2025-06-12 00:04:22 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 47
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件ID: 47, 文件信息: {'id': 47, 'folder_id': 4, 'filename': '3 - 副本 (20) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (20) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (20) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (20) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (20) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:22 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (20) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:22 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\6fc2f6a250fa0227a1761a5f4d600e85_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6fc2f6a250fa0227a1761a5f4d600e85_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6fc2f6a250fa0227a1761a5f4d600e85_medium.jpg
2025-06-12 00:04:22 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 48
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件ID: 48, 文件信息: {'id': 48, 'folder_id': 4, 'filename': '3 - 副本 (20) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (20) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (20) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (20) - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (20) - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:22 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (20) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:22 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\9f2dc639240f135864d7da2e3450403b_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9f2dc639240f135864d7da2e3450403b_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9f2dc639240f135864d7da2e3450403b_medium.jpg
2025-06-12 00:04:22 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 49
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件ID: 49, 文件信息: {'id': 49, 'folder_id': 4, 'filename': '3 - 副本 (20) - 副本.jpg', 'relative_path': '3 - 副本 (20) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (20) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (20) - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (20) - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:22 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (20) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:22 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\10903216d7fd541d368d66adc7c4664b_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\10903216d7fd541d368d66adc7c4664b_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\10903216d7fd541d368d66adc7c4664b_medium.jpg
2025-06-12 00:04:22 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 50
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件ID: 50, 文件信息: {'id': 50, 'folder_id': 4, 'filename': '3 - 副本 (21) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (21) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (21) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (21) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (21) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:22 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (21) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:22 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\44cd2840a5e895d8e50b6040f9cf1633_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\44cd2840a5e895d8e50b6040f9cf1633_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\44cd2840a5e895d8e50b6040f9cf1633_medium.jpg
2025-06-12 00:04:22 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 51
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件ID: 51, 文件信息: {'id': 51, 'folder_id': 4, 'filename': '3 - 副本 (21) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (21) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (21) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (21) - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (21) - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:22 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (21) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:22 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\f53be811687937a510f6825deb6cbb2e_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f53be811687937a510f6825deb6cbb2e_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f53be811687937a510f6825deb6cbb2e_medium.jpg
2025-06-12 00:04:22 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 52
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件ID: 52, 文件信息: {'id': 52, 'folder_id': 4, 'filename': '3 - 副本 (21) - 副本.jpg', 'relative_path': '3 - 副本 (21) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (21) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (21) - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (21) - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:22 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (21) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:22 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\bc9b519211f5171b092be24b7767c59b_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\bc9b519211f5171b092be24b7767c59b_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\bc9b519211f5171b092be24b7767c59b_medium.jpg
2025-06-12 00:04:22 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 53
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件ID: 53, 文件信息: {'id': 53, 'folder_id': 4, 'filename': '3 - 副本 (22) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (22) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (22) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (22) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (22) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:22 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (22) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:22 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\7730620ba3e208b5b4437bb982430606_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7730620ba3e208b5b4437bb982430606_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7730620ba3e208b5b4437bb982430606_medium.jpg
2025-06-12 00:04:22 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 54
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件ID: 54, 文件信息: {'id': 54, 'folder_id': 4, 'filename': '3 - 副本 (22) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (22) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (22) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (22) - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (22) - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:22 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (22) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:22 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\f2e270a043a126a37430bdb4a8387bc6_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f2e270a043a126a37430bdb4a8387bc6_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f2e270a043a126a37430bdb4a8387bc6_medium.jpg
2025-06-12 00:04:22 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 55
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件ID: 55, 文件信息: {'id': 55, 'folder_id': 4, 'filename': '3 - 副本 (22) - 副本.jpg', 'relative_path': '3 - 副本 (22) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (22) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (22) - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (22) - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:22 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (22) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:22 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ff5d06397dbb96c07e9185356c09e5de_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ff5d06397dbb96c07e9185356c09e5de_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ff5d06397dbb96c07e9185356c09e5de_medium.jpg
2025-06-12 00:04:22 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 56
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件ID: 56, 文件信息: {'id': 56, 'folder_id': 4, 'filename': '3 - 副本 (23) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (23) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (23) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (23) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (23) - 副本 - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:22 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (23) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:22 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ef0a79b6af41a199715a5298677b8c87_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ef0a79b6af41a199715a5298677b8c87_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ef0a79b6af41a199715a5298677b8c87_medium.jpg
2025-06-12 00:04:22 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 57
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件ID: 57, 文件信息: {'id': 57, 'folder_id': 4, 'filename': '3 - 副本 (23) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (23) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (23) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (23) - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (23) - 副本 - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:22 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (23) - 副本 - 副本.jpg, 尺寸: medium
2025-06-12 00:04:22 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\11c22a2a7d0e8e14f705d9e5d6fc0e88_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\11c22a2a7d0e8e14f705d9e5d6fc0e88_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\11c22a2a7d0e8e14f705d9e5d6fc0e88_medium.jpg
2025-06-12 00:04:22 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 58
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件ID: 58, 文件信息: {'id': 58, 'folder_id': 4, 'filename': '3 - 副本 (23) - 副本.jpg', 'relative_path': '3 - 副本 (23) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\3 - 副本 (23) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (23) - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (23) - 副本.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:22 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (23) - 副本.jpg, 尺寸: medium
2025-06-12 00:04:22 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\36a786fedbae4cf6806d4f9be1a52708_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\36a786fedbae4cf6806d4f9be1a52708_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\36a786fedbae4cf6806d4f9be1a52708_medium.jpg
2025-06-12 00:04:22 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 10
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件ID: 10, 文件信息: {'id': 10, 'folder_id': 4, 'filename': 'wechat_2025-06-08_191023_815.png', 'relative_path': 'wechat_2025-06-08_191023_815.png', 'file_size': 84610, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-06-08T19:10:33', 'created_at': '2025-06-08T19:10:49', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\wechat_2025-06-08_191023_815.png', 'current_size': 84610, 'current_modified': '2025-06-08T19:10:33.017932', 'exists': True}
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件名: wechat_2025-06-08_191023_815.png
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:22 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\wechat_2025-06-08_191023_815.png
2025-06-12 00:04:22 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:22 - APIServer - INFO - 开始生成缩略图: D:\测试2\wechat_2025-06-08_191023_815.png, 尺寸: medium
2025-06-12 00:04:22 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\82b4852d83cf7ec68f0fce4f15e526dd_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\82b4852d83cf7ec68f0fce4f15e526dd_medium.jpg
2025-06-12 00:04:22 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\82b4852d83cf7ec68f0fce4f15e526dd_medium.jpg
2025-06-12 00:04:25 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-12 00:04:25 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-12 00:04:25 - APIServer - INFO - 缩略图请求 - 文件ID: 10, 文件信息: {'id': 10, 'folder_id': 4, 'filename': 'wechat_2025-06-08_191023_815.png', 'relative_path': 'wechat_2025-06-08_191023_815.png', 'file_size': 84610, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-06-08T19:10:33', 'created_at': '2025-06-08T19:10:49', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\wechat_2025-06-08_191023_815.png', 'current_size': 84610, 'current_modified': '2025-06-08T19:10:33.017932', 'exists': True}
2025-06-12 00:04:25 - APIServer - INFO - 缩略图请求 - 文件名: wechat_2025-06-08_191023_815.png
2025-06-12 00:04:25 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:25 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:25 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\wechat_2025-06-08_191023_815.png
2025-06-12 00:04:25 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:25 - APIServer - INFO - 开始生成缩略图: D:\测试2\wechat_2025-06-08_191023_815.png, 尺寸: large
2025-06-12 00:04:25 - APIServer - INFO - 缩略图路径: data\thumbnails\large\82b4852d83cf7ec68f0fce4f15e526dd_large.jpg
2025-06-12 00:04:25 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\82b4852d83cf7ec68f0fce4f15e526dd_large.jpg
2025-06-12 00:04:25 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\82b4852d83cf7ec68f0fce4f15e526dd_large.jpg
2025-06-12 00:04:25 - APIServer - INFO - 缩略图请求 - 文件ID: 10, 文件信息: {'id': 10, 'folder_id': 4, 'filename': 'wechat_2025-06-08_191023_815.png', 'relative_path': 'wechat_2025-06-08_191023_815.png', 'file_size': 84610, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 2}, 'timestamps': {'file_modified': '2025-06-08T19:10:33', 'created_at': '2025-06-08T19:10:49', 'last_accessed': '2025-06-10T20:19:33'}, 'full_path': 'D:\\测试2\\wechat_2025-06-08_191023_815.png', 'current_size': 84610, 'current_modified': '2025-06-08T19:10:33.017932', 'exists': True}
2025-06-12 00:04:25 - APIServer - INFO - 缩略图请求 - 文件名: wechat_2025-06-08_191023_815.png
2025-06-12 00:04:25 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:04:25 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:04:25 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\wechat_2025-06-08_191023_815.png
2025-06-12 00:04:25 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:04:25 - APIServer - INFO - 开始生成缩略图: D:\测试2\wechat_2025-06-08_191023_815.png, 尺寸: large
2025-06-12 00:04:25 - APIServer - INFO - 缩略图路径: data\thumbnails\large\82b4852d83cf7ec68f0fce4f15e526dd_large.jpg
2025-06-12 00:04:25 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\82b4852d83cf7ec68f0fce4f15e526dd_large.jpg
2025-06-12 00:04:25 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\82b4852d83cf7ec68f0fce4f15e526dd_large.jpg
2025-06-12 00:04:40 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:04:40 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:04:40 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:04:40 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:04:40 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:04:40 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:08:44 - APIServer - INFO - API服务器已停止
2025-06-12 00:13:40 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 00:13:40 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-12 00:13:43 - APIServer - INFO - API服务器已停止
2025-06-12 00:13:43 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 00:13:50 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:13:50 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:13:50 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:13:50 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:13:50 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:13:50 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:13:52 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:13:52 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:13:52 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:13:52 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:13:52 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:13:52 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:13:57 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-12 00:13:57 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 3}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': '2025-06-10T20:19:25'}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-12 00:13:57 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-12 00:13:57 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:13:57 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:13:57 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-12 00:13:57 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:13:57 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-12 00:13:57 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-12 00:13:57 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-12 00:13:57 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-12 00:13:57 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-12 00:13:57 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 3}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': '2025-06-10T20:19:25'}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-12 00:13:57 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-12 00:13:57 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 00:13:57 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 00:13:57 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-12 00:13:57 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 00:13:57 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-12 00:13:57 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-12 00:13:57 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-12 00:13:57 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-12 00:13:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:13:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:13:59 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:13:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:13:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:14:00 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:14:02 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:14:02 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:14:02 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:14:02 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:14:02 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:14:02 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:14:13 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:14:13 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:14:13 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:14:13 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:14:13 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:14:13 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:19:45 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 00:19:49 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:19:49 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:19:49 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:19:49 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:19:49 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:19:49 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:19:52 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:19:52 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:19:52 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:19:52 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:19:52 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:19:52 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:20:14 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:20:14 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:20:14 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:20:14 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:20:14 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:20:14 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:26:30 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:26:30 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:26:30 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:26:30 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:26:30 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:26:30 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:27:39 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:27:39 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:27:39 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:27:47 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:27:47 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:27:47 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:27:47 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:27:47 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:27:47 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:06 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:06 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:06 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:06 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:06 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:06 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:12 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:12 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:12 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:12 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:12 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:12 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:17 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:17 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:17 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:17 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:17 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:17 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:17 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:17 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:17 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:17 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:17 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:17 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:17 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:17 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:17 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:17 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:17 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:17 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:27 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:27 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:27 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:27 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:27 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:27 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:28 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:28 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:28 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:28 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:28 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:28 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:30 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:30 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:30 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:30 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:28:30 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:28:30 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:28:43 - APIServer - INFO - API服务器已停止
2025-06-12 00:40:34 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 00:40:39 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:40:39 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:40:39 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:40:39 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:40:39 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:40:39 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:46:37 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 00:46:37 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-12 00:46:39 - APIServer - INFO - API服务器已停止
2025-06-12 00:46:41 - APIServer - INFO - API服务器已停止
2025-06-12 00:46:47 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 00:46:52 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:46:52 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:46:52 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:46:52 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:46:52 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:46:52 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:47:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:47:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:47:43 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:47:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:47:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:47:43 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:47:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:47:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:47:59 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:47:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:47:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:47:59 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:48:16 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:48:16 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:48:16 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:48:16 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:48:16 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:48:16 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:50:17 - APIServer - INFO - API服务器已停止
2025-06-12 00:50:30 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 00:50:35 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:50:35 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:50:35 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:50:35 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:50:35 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:50:35 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:50:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:50:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:50:43 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 00:50:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 00:50:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-12 00:50:43 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 08:35:31 - APIServer - INFO - 加密服务初始化成功
2025-06-12 08:35:31 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 08:44:40 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 08:57:34 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 09:06:28 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 09:17:40 - APIServer - INFO - 加密服务初始化成功
2025-06-12 09:17:41 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 09:33:09 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 09:34:00 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 09:35:10 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:35:10 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 1, 'name': '示例图片', 'path': 'C:/shared/images', 'description': '包含示例图片文件的文件夹', 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 0, 'total_size': 0, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T08:34:19.901993', 'updated_at': '2025-06-12T00:34:19', 'last_scanned': None, 'file_count': 2}, {'id': 2, 'name': '设计文件', 'path': 'C:/shared/design', 'description': '包含设计文件的文件夹', 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 0, 'total_size': 0, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T08:34:19.902991', 'updated_at': '2025-06-12T00:34:19', 'last_scanned': None, 'file_count': 2}, {'id': 3, 'name': '照片集', 'path': 'C:/shared/photos', 'description': '包含照片的文件夹', 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 0, 'total_size': 0, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T08:34:19.902991', 'updated_at': '2025-06-12T00:34:19', 'last_scanned': None, 'file_count': 1}]}
2025-06-12 09:35:10 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 09:35:10 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:35:10 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 1, 'name': '示例图片', 'path': 'C:/shared/images', 'description': '包含示例图片文件的文件夹', 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 0, 'total_size': 0, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T08:34:19.901993', 'updated_at': '2025-06-12T00:34:19', 'last_scanned': None, 'file_count': 2}, {'id': 2, 'name': '设计文件', 'path': 'C:/shared/design', 'description': '包含设计文件的文件夹', 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 0, 'total_size': 0, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T08:34:19.902991', 'updated_at': '2025-06-12T00:34:19', 'last_scanned': None, 'file_count': 2}, {'id': 3, 'name': '照片集', 'path': 'C:/shared/photos', 'description': '包含照片的文件夹', 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 0, 'total_size': 0, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T08:34:19.902991', 'updated_at': '2025-06-12T00:34:19', 'last_scanned': None, 'file_count': 1}]}
2025-06-12 09:35:10 - APIServer - INFO - 返回 3 个文件夹
2025-06-12 09:35:15 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-12 09:35:15 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 3, 'filename': 'photo1.png', 'relative_path': 'photos/photo1.png', 'file_size': 2048000, 'file_hash': None, 'mime_type': 'image/png', 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': True, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-12T08:34:19.902991', 'created_at': '2025-06-12T08:34:19.902991', 'last_accessed': None}, 'full_path': 'C:/shared/photos\\photos/photo1.png', 'exists': False}
2025-06-12 09:35:15 - APIServer - INFO - 缩略图请求 - 文件名: photo1.png
2025-06-12 09:35:15 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'exists']
2025-06-12 09:35:15 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 09:35:15 - APIServer - INFO - 缩略图请求 - 完整路径: C:/shared/photos\photos/photo1.png
2025-06-12 09:35:15 - APIServer - WARNING - 缩略图请求失败 - 文件路径不存在: C:/shared/photos\photos/photo1.png
2025-06-12 09:35:15 - APIServer - ERROR - 获取缩略图失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-12 09:35:16 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-12 09:35:16 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 3, 'filename': 'photo1.png', 'relative_path': 'photos/photo1.png', 'file_size': 2048000, 'file_hash': None, 'mime_type': 'image/png', 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': True, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-12T08:34:19.902991', 'created_at': '2025-06-12T08:34:19.902991', 'last_accessed': None}, 'full_path': 'C:/shared/photos\\photos/photo1.png', 'exists': False}
2025-06-12 09:35:16 - APIServer - INFO - 缩略图请求 - 文件名: photo1.png
2025-06-12 09:35:16 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'exists']
2025-06-12 09:35:16 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 09:35:16 - APIServer - INFO - 缩略图请求 - 完整路径: C:/shared/photos\photos/photo1.png
2025-06-12 09:35:16 - APIServer - WARNING - 缩略图请求失败 - 文件路径不存在: C:/shared/photos\photos/photo1.png
2025-06-12 09:35:16 - APIServer - ERROR - 获取缩略图失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-12 09:35:18 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-12 09:35:18 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-12 09:35:18 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 3, 'filename': 'photo1.png', 'relative_path': 'photos/photo1.png', 'file_size': 2048000, 'file_hash': None, 'mime_type': 'image/png', 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': True, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-12T08:34:19.902991', 'created_at': '2025-06-12T08:34:19.902991', 'last_accessed': None}, 'full_path': 'C:/shared/photos\\photos/photo1.png', 'exists': False}
2025-06-12 09:35:18 - APIServer - INFO - 缩略图请求 - 文件名: photo1.png
2025-06-12 09:35:18 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'exists']
2025-06-12 09:35:18 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 09:35:18 - APIServer - INFO - 缩略图请求 - 完整路径: C:/shared/photos\photos/photo1.png
2025-06-12 09:35:18 - APIServer - WARNING - 缩略图请求失败 - 文件路径不存在: C:/shared/photos\photos/photo1.png
2025-06-12 09:35:18 - APIServer - ERROR - 获取缩略图失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-12 09:35:18 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 3, 'filename': 'photo1.png', 'relative_path': 'photos/photo1.png', 'file_size': 2048000, 'file_hash': None, 'mime_type': 'image/png', 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': True, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-12T08:34:19.902991', 'created_at': '2025-06-12T08:34:19.902991', 'last_accessed': None}, 'full_path': 'C:/shared/photos\\photos/photo1.png', 'exists': False}
2025-06-12 09:35:18 - APIServer - INFO - 缩略图请求 - 文件名: photo1.png
2025-06-12 09:35:18 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'exists']
2025-06-12 09:35:18 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 09:35:18 - APIServer - INFO - 缩略图请求 - 完整路径: C:/shared/photos\photos/photo1.png
2025-06-12 09:35:18 - APIServer - WARNING - 缩略图请求失败 - 文件路径不存在: C:/shared/photos\photos/photo1.png
2025-06-12 09:35:18 - APIServer - ERROR - 获取缩略图失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-12 09:35:42 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:35:42 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': []}
2025-06-12 09:35:42 - APIServer - INFO - 返回 0 个文件夹
2025-06-12 09:35:42 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:35:42 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': []}
2025-06-12 09:35:42 - APIServer - INFO - 返回 0 个文件夹
2025-06-12 09:35:42 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:35:42 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': []}
2025-06-12 09:35:42 - APIServer - INFO - 返回 0 个文件夹
2025-06-12 09:35:42 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:35:42 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': []}
2025-06-12 09:35:42 - APIServer - INFO - 返回 0 个文件夹
2025-06-12 09:36:26 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:36:26 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': []}
2025-06-12 09:36:26 - APIServer - INFO - 返回 0 个文件夹
2025-06-12 09:36:26 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:36:26 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': []}
2025-06-12 09:36:26 - APIServer - INFO - 返回 0 个文件夹
2025-06-12 09:36:53 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:36:53 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:36:53 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:36:53 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:36:53 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:36:53 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:36:55 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 6
2025-06-12 09:36:55 - APIServer - INFO - 缩略图请求 - 文件ID: 6, 文件信息: {'id': 6, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T01:36:37', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 09:36:55 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 09:36:55 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 09:36:55 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 09:36:55 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 09:36:55 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 09:36:55 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png, 尺寸: medium
2025-06-12 09:36:55 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\c62a79e02f8332192773187f1c5e8ee8_medium.jpg
2025-06-12 09:36:55 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c62a79e02f8332192773187f1c5e8ee8_medium.jpg
2025-06-12 09:36:55 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c62a79e02f8332192773187f1c5e8ee8_medium.jpg
2025-06-12 09:36:55 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 7
2025-06-12 09:36:55 - APIServer - INFO - 缩略图请求 - 文件ID: 7, 文件信息: {'id': 7, 'folder_id': 4, 'filename': '466616e4-dd38-4ca5-b729-63729dd7dfd6.png', 'relative_path': '466616e4-dd38-4ca5-b729-63729dd7dfd6.png', 'file_size': 1110385, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T16:40:13.405224', 'created_at': '2025-06-12T01:36:37', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\466616e4-dd38-4ca5-b729-63729dd7dfd6.png', 'current_size': 1110385, 'current_modified': '2025-06-04T16:40:13.405224', 'exists': True}
2025-06-12 09:36:55 - APIServer - INFO - 缩略图请求 - 文件名: 466616e4-dd38-4ca5-b729-63729dd7dfd6.png
2025-06-12 09:36:55 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 09:36:55 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 09:36:55 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\466616e4-dd38-4ca5-b729-63729dd7dfd6.png
2025-06-12 09:36:55 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 09:36:55 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\466616e4-dd38-4ca5-b729-63729dd7dfd6.png, 尺寸: medium
2025-06-12 09:36:55 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\17b6779c53dbfca5265cbfa2e0a76d86_medium.jpg
2025-06-12 09:36:55 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\17b6779c53dbfca5265cbfa2e0a76d86_medium.jpg
2025-06-12 09:36:55 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\17b6779c53dbfca5265cbfa2e0a76d86_medium.jpg
2025-06-12 09:36:56 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-12 09:36:56 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-12 09:36:56 - APIServer - INFO - 缩略图请求 - 文件ID: 6, 文件信息: {'id': 6, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T01:36:37', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 09:36:56 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 09:36:56 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 09:36:56 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 09:36:56 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 09:36:56 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 09:36:56 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png, 尺寸: large
2025-06-12 09:36:56 - APIServer - INFO - 缩略图路径: data\thumbnails\large\c62a79e02f8332192773187f1c5e8ee8_large.jpg
2025-06-12 09:36:56 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\c62a79e02f8332192773187f1c5e8ee8_large.jpg
2025-06-12 09:36:56 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\c62a79e02f8332192773187f1c5e8ee8_large.jpg
2025-06-12 09:36:56 - APIServer - INFO - 缩略图请求 - 文件ID: 6, 文件信息: {'id': 6, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T01:36:37', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 09:36:56 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 09:36:56 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 09:36:56 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 09:36:56 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 09:36:56 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 09:36:56 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png, 尺寸: large
2025-06-12 09:36:56 - APIServer - INFO - 缩略图路径: data\thumbnails\large\c62a79e02f8332192773187f1c5e8ee8_large.jpg
2025-06-12 09:36:56 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\c62a79e02f8332192773187f1c5e8ee8_large.jpg
2025-06-12 09:36:56 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\c62a79e02f8332192773187f1c5e8ee8_large.jpg
2025-06-12 09:39:55 - APIServer - INFO - 加密服务初始化成功
2025-06-12 09:39:56 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 09:39:56 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-12 09:44:19 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 09:44:28 - APIServer - INFO - 加密服务初始化成功
2025-06-12 09:44:28 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 09:46:29 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 09:46:37 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:46:37 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:46:37 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:46:37 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:46:37 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:46:37 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:47:37 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:47:37 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:47:37 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:47:37 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:47:37 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:47:37 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:49:19 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:49:19 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:49:19 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:49:19 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:49:19 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:49:19 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:49:20 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:49:20 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:49:20 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:49:20 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:49:20 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:49:20 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:51:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:51:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:51:43 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:51:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:51:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:51:43 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:51:44 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 6
2025-06-12 09:51:44 - APIServer - INFO - 缩略图请求 - 文件ID: 6, 文件信息: {'id': 6, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T01:36:37', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 09:51:44 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 09:51:44 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 09:51:44 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 09:51:44 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 09:51:44 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 09:51:44 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png, 尺寸: medium
2025-06-12 09:51:44 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\c62a79e02f8332192773187f1c5e8ee8_medium.jpg
2025-06-12 09:51:44 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c62a79e02f8332192773187f1c5e8ee8_medium.jpg
2025-06-12 09:51:44 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c62a79e02f8332192773187f1c5e8ee8_medium.jpg
2025-06-12 09:51:44 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 7
2025-06-12 09:51:44 - APIServer - INFO - 缩略图请求 - 文件ID: 7, 文件信息: {'id': 7, 'folder_id': 4, 'filename': '466616e4-dd38-4ca5-b729-63729dd7dfd6.png', 'relative_path': '466616e4-dd38-4ca5-b729-63729dd7dfd6.png', 'file_size': 1110385, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T16:40:13.405224', 'created_at': '2025-06-12T01:36:37', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\466616e4-dd38-4ca5-b729-63729dd7dfd6.png', 'current_size': 1110385, 'current_modified': '2025-06-04T16:40:13.405224', 'exists': True}
2025-06-12 09:51:44 - APIServer - INFO - 缩略图请求 - 文件名: 466616e4-dd38-4ca5-b729-63729dd7dfd6.png
2025-06-12 09:51:44 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 09:51:44 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 09:51:44 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\466616e4-dd38-4ca5-b729-63729dd7dfd6.png
2025-06-12 09:51:44 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 09:51:44 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\466616e4-dd38-4ca5-b729-63729dd7dfd6.png, 尺寸: medium
2025-06-12 09:51:44 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\17b6779c53dbfca5265cbfa2e0a76d86_medium.jpg
2025-06-12 09:51:44 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\17b6779c53dbfca5265cbfa2e0a76d86_medium.jpg
2025-06-12 09:51:44 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\17b6779c53dbfca5265cbfa2e0a76d86_medium.jpg
2025-06-12 09:51:45 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-12 09:51:45 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-12 09:51:45 - APIServer - INFO - 缩略图请求 - 文件ID: 6, 文件信息: {'id': 6, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T01:36:37', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 09:51:45 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 09:51:45 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 09:51:45 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 09:51:45 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 09:51:45 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 09:51:45 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png, 尺寸: large
2025-06-12 09:51:45 - APIServer - INFO - 缩略图路径: data\thumbnails\large\c62a79e02f8332192773187f1c5e8ee8_large.jpg
2025-06-12 09:51:45 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\c62a79e02f8332192773187f1c5e8ee8_large.jpg
2025-06-12 09:51:45 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\c62a79e02f8332192773187f1c5e8ee8_large.jpg
2025-06-12 09:51:45 - APIServer - INFO - 缩略图请求 - 文件ID: 6, 文件信息: {'id': 6, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T01:36:37', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 09:51:45 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 09:51:45 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 09:51:45 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 09:51:45 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 09:51:45 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 09:51:45 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png, 尺寸: large
2025-06-12 09:51:45 - APIServer - INFO - 缩略图路径: data\thumbnails\large\c62a79e02f8332192773187f1c5e8ee8_large.jpg
2025-06-12 09:51:45 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\c62a79e02f8332192773187f1c5e8ee8_large.jpg
2025-06-12 09:51:45 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\c62a79e02f8332192773187f1c5e8ee8_large.jpg
2025-06-12 09:56:03 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:56:03 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:56:03 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:56:03 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:56:03 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:56:03 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:56:05 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:56:05 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:56:05 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 09:56:05 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 09:56:05 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 09:56:05 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:00:31 - APIServer - INFO - 加密服务初始化成功
2025-06-12 10:00:31 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 10:02:27 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:02:27 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:02:27 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:02:48 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:02:48 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:02:48 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:03:30 - APIServer - INFO - 加密服务初始化成功
2025-06-12 10:03:30 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 10:03:58 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:03:58 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:03:58 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:04:02 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:04:02 - APIServer - INFO - 成功获取 0 条下载记录
2025-06-12 10:04:09 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:04:09 - APIServer - INFO - 成功获取 1 条下载记录
2025-06-12 10:04:24 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:04:24 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:04:24 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:04:28 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:04:28 - APIServer - INFO - 成功获取 1 条下载记录
2025-06-12 10:04:34 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:04:34 - APIServer - INFO - 成功获取 2 条下载记录
2025-06-12 10:05:51 - APIServer - INFO - 加密服务初始化成功
2025-06-12 10:05:52 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 10:06:05 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:06:05 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:06:05 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:06:05 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:06:05 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:06:05 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:06:11 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:06:11 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:06:11 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:06:11 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:06:11 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:06:11 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:08:29 - APIServer - INFO - 加密服务初始化成功
2025-06-12 10:08:30 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 10:08:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:08:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:08:59 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:09:03 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:09:03 - APIServer - INFO - 成功获取 0 条下载记录
2025-06-12 10:09:10 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:09:10 - APIServer - INFO - 成功获取 0 条下载记录
2025-06-12 10:09:46 - APIServer - INFO - 加密服务初始化成功
2025-06-12 10:09:46 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 10:10:24 - APIServer - ERROR - 登录失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-06-12 10:10:57 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:10:57 - APIServer - INFO - 成功获取 0 条下载记录
2025-06-12 10:12:45 - APIServer - INFO - 加密服务初始化成功
2025-06-12 10:12:45 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 10:13:15 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:13:15 - APIServer - INFO - 成功获取 1 条下载记录
2025-06-12 10:13:32 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:13:32 - APIServer - INFO - 成功获取 2 条下载记录
2025-06-12 10:13:50 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:13:50 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:13:50 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:13:54 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:13:54 - APIServer - INFO - 成功获取 2 条下载记录
2025-06-12 10:14:00 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:14:00 - APIServer - INFO - 成功获取 3 条下载记录
2025-06-12 10:14:32 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 10:14:32 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-12 10:14:43 - APIServer - INFO - API服务器已停止
2025-06-12 10:14:56 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 10:16:39 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:16:39 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:16:39 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:16:39 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:16:39 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:16:39 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:16:39 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:16:39 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:16:39 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:16:40 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:16:40 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:16:40 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:16:41 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:16:41 - APIServer - INFO - 成功获取 3 条下载记录
2025-06-12 10:16:41 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:16:41 - APIServer - INFO - 成功获取 3 条下载记录
2025-06-12 10:16:41 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:16:41 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:16:41 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:16:41 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:16:41 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:16:41 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:16:41 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:16:41 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:16:41 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:16:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 7
2025-06-12 10:16:42 - APIServer - INFO - 缩略图请求 - 文件ID: 7, 文件信息: {'id': 7, 'folder_id': 4, 'filename': '466616e4-dd38-4ca5-b729-63729dd7dfd6.png', 'relative_path': '466616e4-dd38-4ca5-b729-63729dd7dfd6.png', 'file_size': 1110385, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T16:40:13.405224', 'created_at': '2025-06-12T01:36:37', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\466616e4-dd38-4ca5-b729-63729dd7dfd6.png', 'current_size': 1110385, 'current_modified': '2025-06-04T16:40:13.405224', 'exists': True}
2025-06-12 10:16:42 - APIServer - INFO - 缩略图请求 - 文件名: 466616e4-dd38-4ca5-b729-63729dd7dfd6.png
2025-06-12 10:16:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:16:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:16:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\466616e4-dd38-4ca5-b729-63729dd7dfd6.png
2025-06-12 10:16:42 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:16:42 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\466616e4-dd38-4ca5-b729-63729dd7dfd6.png, 尺寸: medium
2025-06-12 10:16:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\17b6779c53dbfca5265cbfa2e0a76d86_medium.jpg
2025-06-12 10:16:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\17b6779c53dbfca5265cbfa2e0a76d86_medium.jpg
2025-06-12 10:16:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\17b6779c53dbfca5265cbfa2e0a76d86_medium.jpg
2025-06-12 10:16:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 6
2025-06-12 10:16:42 - APIServer - INFO - 缩略图请求 - 文件ID: 6, 文件信息: {'id': 6, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T01:36:37', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:16:42 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 10:16:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:16:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:16:42 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 10:16:42 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:16:42 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png, 尺寸: medium
2025-06-12 10:16:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\c62a79e02f8332192773187f1c5e8ee8_medium.jpg
2025-06-12 10:16:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c62a79e02f8332192773187f1c5e8ee8_medium.jpg
2025-06-12 10:16:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c62a79e02f8332192773187f1c5e8ee8_medium.jpg
2025-06-12 10:16:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:16:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:16:43 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:16:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:16:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:16:43 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:16:56 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:16:56 - APIServer - INFO - 成功获取 4 条下载记录
2025-06-12 10:16:56 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:16:56 - APIServer - INFO - 成功获取 4 条下载记录
2025-06-12 10:16:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:16:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:16:59 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:16:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:16:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:16:59 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:17:00 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:17:00 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:17:00 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:17:00 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:17:00 - APIServer - INFO - 成功获取 4 条下载记录
2025-06-12 10:17:00 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:17:00 - APIServer - INFO - 成功获取 4 条下载记录
2025-06-12 10:17:00 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:17:00 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:17:00 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:17:14 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:17:14 - APIServer - INFO - 成功获取 4 条下载记录
2025-06-12 10:17:14 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:17:14 - APIServer - INFO - 成功获取 4 条下载记录
2025-06-12 10:17:14 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:17:14 - APIServer - INFO - 成功获取 4 条下载记录
2025-06-12 10:17:14 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:17:14 - APIServer - INFO - 成功获取 4 条下载记录
2025-06-12 10:20:30 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:20:30 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:20:30 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:20:30 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:20:30 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 2179784, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T01:36:47', 'last_scanned': '2025-06-12T09:36:47.413186', 'file_count': 2}]}
2025-06-12 10:20:30 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:20:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 6
2025-06-12 10:20:32 - APIServer - INFO - 缩略图请求 - 文件ID: 6, 文件信息: {'id': 6, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T01:36:37', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:32 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 10:20:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:32 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png
2025-06-12 10:20:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:32 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f.png, 尺寸: medium
2025-06-12 10:20:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\c62a79e02f8332192773187f1c5e8ee8_medium.jpg
2025-06-12 10:20:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c62a79e02f8332192773187f1c5e8ee8_medium.jpg
2025-06-12 10:20:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c62a79e02f8332192773187f1c5e8ee8_medium.jpg
2025-06-12 10:20:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 7
2025-06-12 10:20:32 - APIServer - INFO - 缩略图请求 - 文件ID: 7, 文件信息: {'id': 7, 'folder_id': 4, 'filename': '466616e4-dd38-4ca5-b729-63729dd7dfd6.png', 'relative_path': '466616e4-dd38-4ca5-b729-63729dd7dfd6.png', 'file_size': 1110385, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T16:40:13.405224', 'created_at': '2025-06-12T01:36:37', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\466616e4-dd38-4ca5-b729-63729dd7dfd6.png', 'current_size': 1110385, 'current_modified': '2025-06-04T16:40:13.405224', 'exists': True}
2025-06-12 10:20:32 - APIServer - INFO - 缩略图请求 - 文件名: 466616e4-dd38-4ca5-b729-63729dd7dfd6.png
2025-06-12 10:20:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:32 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\466616e4-dd38-4ca5-b729-63729dd7dfd6.png
2025-06-12 10:20:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:32 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\466616e4-dd38-4ca5-b729-63729dd7dfd6.png, 尺寸: medium
2025-06-12 10:20:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\17b6779c53dbfca5265cbfa2e0a76d86_medium.jpg
2025-06-12 10:20:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\17b6779c53dbfca5265cbfa2e0a76d86_medium.jpg
2025-06-12 10:20:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\17b6779c53dbfca5265cbfa2e0a76d86_medium.jpg
2025-06-12 10:20:45 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:20:45 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 316, 'total_size': 344487844, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T02:20:38', 'last_scanned': '2025-06-12T10:20:38.659662', 'file_count': 316}]}
2025-06-12 10:20:45 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:20:45 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:20:45 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 316, 'total_size': 344487844, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T02:20:38', 'last_scanned': '2025-06-12T10:20:38.659662', 'file_count': 316}]}
2025-06-12 10:20:45 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:20:46 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 8
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件ID: 8, 文件信息: {'id': 8, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (10) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (10) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (10) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (10) - 副本.png
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (10) - 副本.png
2025-06-12 10:20:46 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:46 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (10) - 副本.png, 尺寸: medium
2025-06-12 10:20:46 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\d990d66bee17f34e854348ac66d0e4e9_medium.jpg
2025-06-12 10:20:46 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d990d66bee17f34e854348ac66d0e4e9_medium.jpg
2025-06-12 10:20:46 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d990d66bee17f34e854348ac66d0e4e9_medium.jpg
2025-06-12 10:20:46 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 10
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件ID: 10, 文件信息: {'id': 10, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (100) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (100) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (100) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (100) - 副本.png
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (100) - 副本.png
2025-06-12 10:20:46 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:46 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (100) - 副本.png, 尺寸: medium
2025-06-12 10:20:46 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\5863f8e9a817af604c1a8c54dfd9bf54_medium.jpg
2025-06-12 10:20:46 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\5863f8e9a817af604c1a8c54dfd9bf54_medium.jpg
2025-06-12 10:20:46 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\5863f8e9a817af604c1a8c54dfd9bf54_medium.jpg
2025-06-12 10:20:46 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 9
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件ID: 9, 文件信息: {'id': 9, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (10).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (10).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (10).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (10).png
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (10).png
2025-06-12 10:20:46 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:46 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (10).png, 尺寸: medium
2025-06-12 10:20:46 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\3c2995fe596ffc566f7f1e0689536800_medium.jpg
2025-06-12 10:20:46 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3c2995fe596ffc566f7f1e0689536800_medium.jpg
2025-06-12 10:20:46 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3c2995fe596ffc566f7f1e0689536800_medium.jpg
2025-06-12 10:20:46 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 11
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件ID: 11, 文件信息: {'id': 11, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (101) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (101) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (101) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (101) - 副本.png
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:46 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (101) - 副本.png
2025-06-12 10:20:46 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:46 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (101) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\9cd05f69f89383175381da157d3f920c_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9cd05f69f89383175381da157d3f920c_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9cd05f69f89383175381da157d3f920c_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 13
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 13, 文件信息: {'id': 13, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (103) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (103) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (103) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (103) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (103) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (103) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\42c8a424714b2d906bf9a99642f22225_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\42c8a424714b2d906bf9a99642f22225_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\42c8a424714b2d906bf9a99642f22225_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 12
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 12, 文件信息: {'id': 12, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (102) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (102) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (102) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (102) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (102) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (102) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\c3a1f2f637e0489543c08eb7828767ef_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c3a1f2f637e0489543c08eb7828767ef_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c3a1f2f637e0489543c08eb7828767ef_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 14
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 14, 文件信息: {'id': 14, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (104) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (104) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (104) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (104) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (104) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (104) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\f14829b3e2069b9ebaad13b05f458544_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f14829b3e2069b9ebaad13b05f458544_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f14829b3e2069b9ebaad13b05f458544_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 15
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 15, 文件信息: {'id': 15, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (105) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (105) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (105) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (105) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (105) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (105) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\63b491f2ab09f8264c39af971a62dc10_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\63b491f2ab09f8264c39af971a62dc10_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\63b491f2ab09f8264c39af971a62dc10_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 16
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 16, 文件信息: {'id': 16, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (106) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (106) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (106) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (106) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (106) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (106) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ade6d008efd7f4c1600741148e4e0e64_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ade6d008efd7f4c1600741148e4e0e64_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ade6d008efd7f4c1600741148e4e0e64_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 17
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 17, 文件信息: {'id': 17, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (107) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (107) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (107) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (107) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (107) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (107) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\0a6eaa8b3b7cc80685b0f229bb10710f_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0a6eaa8b3b7cc80685b0f229bb10710f_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0a6eaa8b3b7cc80685b0f229bb10710f_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 18
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 18, 文件信息: {'id': 18, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (108) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (108) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (108) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (108) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (108) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (108) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\5326f6229ad36a1d9109f9dc1bd00c42_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\5326f6229ad36a1d9109f9dc1bd00c42_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\5326f6229ad36a1d9109f9dc1bd00c42_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 19
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 19, 文件信息: {'id': 19, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (109) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (109) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (109) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (109) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (109) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (109) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\1e4364de864fd71a968e51d7c0d19780_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\1e4364de864fd71a968e51d7c0d19780_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\1e4364de864fd71a968e51d7c0d19780_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 20
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 20, 文件信息: {'id': 20, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (11) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (11) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (11) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (11) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (11) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (11) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\e301c498df89219d5e0687342ee6f2d9_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\e301c498df89219d5e0687342ee6f2d9_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\e301c498df89219d5e0687342ee6f2d9_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 21
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 21, 文件信息: {'id': 21, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (11).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (11).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (11).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (11).png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (11).png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (11).png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\fac901ac2ca60f8f755eda52b0051022_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\fac901ac2ca60f8f755eda52b0051022_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\fac901ac2ca60f8f755eda52b0051022_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 22
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 22, 文件信息: {'id': 22, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (110) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (110) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (110) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (110) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (110) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (110) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\3000e404cb6b191c1689887c71c806b8_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3000e404cb6b191c1689887c71c806b8_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3000e404cb6b191c1689887c71c806b8_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 23
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 23, 文件信息: {'id': 23, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (111) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (111) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (111) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (111) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (111) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (111) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\3782e308c2e009d26572c598378d69b1_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3782e308c2e009d26572c598378d69b1_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3782e308c2e009d26572c598378d69b1_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 24
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 24, 文件信息: {'id': 24, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (112) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (112) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (112) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (112) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (112) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (112) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\d6d257e0300da9486bb8a62e5cbe008d_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d6d257e0300da9486bb8a62e5cbe008d_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d6d257e0300da9486bb8a62e5cbe008d_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 25
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 25, 文件信息: {'id': 25, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (113) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (113) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (113) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (113) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (113) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (113) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\1ba0e2de503896d49a41d7bb202dc70d_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\1ba0e2de503896d49a41d7bb202dc70d_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\1ba0e2de503896d49a41d7bb202dc70d_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 26
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件ID: 26, 文件信息: {'id': 26, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (114) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (114) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (114) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (114) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:47 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (114) - 副本.png
2025-06-12 10:20:47 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:47 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (114) - 副本.png, 尺寸: medium
2025-06-12 10:20:47 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\9e887c452a5c49cdc6a27e7ab34a933a_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9e887c452a5c49cdc6a27e7ab34a933a_medium.jpg
2025-06-12 10:20:47 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9e887c452a5c49cdc6a27e7ab34a933a_medium.jpg
2025-06-12 10:20:47 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 27
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件ID: 27, 文件信息: {'id': 27, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (115) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (115) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (115) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (115) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (115) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:48 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (115) - 副本.png, 尺寸: medium
2025-06-12 10:20:48 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\53240b95815f6409ffce7e1802ed270f_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\53240b95815f6409ffce7e1802ed270f_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\53240b95815f6409ffce7e1802ed270f_medium.jpg
2025-06-12 10:20:48 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 28
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件ID: 28, 文件信息: {'id': 28, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (116) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (116) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (116) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (116) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (116) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:48 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (116) - 副本.png, 尺寸: medium
2025-06-12 10:20:48 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\a0690aee904c97c91c2acedfd7fdd9f1_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\a0690aee904c97c91c2acedfd7fdd9f1_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\a0690aee904c97c91c2acedfd7fdd9f1_medium.jpg
2025-06-12 10:20:48 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 29
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件ID: 29, 文件信息: {'id': 29, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (117) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (117) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (117) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (117) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (117) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:48 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (117) - 副本.png, 尺寸: medium
2025-06-12 10:20:48 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\d812b880650eb7cf903cc4b3831be484_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d812b880650eb7cf903cc4b3831be484_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d812b880650eb7cf903cc4b3831be484_medium.jpg
2025-06-12 10:20:48 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 30
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件ID: 30, 文件信息: {'id': 30, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (12) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (12) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (12) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (12) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (12) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:48 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (12) - 副本.png, 尺寸: medium
2025-06-12 10:20:48 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\26911270252e0c99e0c8fe436f2c4389_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\26911270252e0c99e0c8fe436f2c4389_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\26911270252e0c99e0c8fe436f2c4389_medium.jpg
2025-06-12 10:20:48 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 31
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件ID: 31, 文件信息: {'id': 31, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (12).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (12).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (12).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (12).png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (12).png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:48 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (12).png, 尺寸: medium
2025-06-12 10:20:48 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\73c0338d6b19ece5def24f4e51b0f6cd_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\73c0338d6b19ece5def24f4e51b0f6cd_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\73c0338d6b19ece5def24f4e51b0f6cd_medium.jpg
2025-06-12 10:20:48 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 32
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件ID: 32, 文件信息: {'id': 32, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (13) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (13) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (13) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (13) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (13) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:48 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (13) - 副本.png, 尺寸: medium
2025-06-12 10:20:48 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\8781d9f7dc1553d61882918c45a6793a_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8781d9f7dc1553d61882918c45a6793a_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8781d9f7dc1553d61882918c45a6793a_medium.jpg
2025-06-12 10:20:48 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 33
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件ID: 33, 文件信息: {'id': 33, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (13).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (13).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (13).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (13).png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (13).png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:48 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (13).png, 尺寸: medium
2025-06-12 10:20:48 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\904ecade46ea1f3824e055fccf652ed8_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\904ecade46ea1f3824e055fccf652ed8_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\904ecade46ea1f3824e055fccf652ed8_medium.jpg
2025-06-12 10:20:48 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 34
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件ID: 34, 文件信息: {'id': 34, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (14) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (14) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (14) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (14) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (14) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:48 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (14) - 副本.png, 尺寸: medium
2025-06-12 10:20:48 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\082aa9c236658c1184bcad0c2e784988_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\082aa9c236658c1184bcad0c2e784988_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\082aa9c236658c1184bcad0c2e784988_medium.jpg
2025-06-12 10:20:48 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 35
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件ID: 35, 文件信息: {'id': 35, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (14).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (14).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (14).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (14).png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (14).png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:48 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (14).png, 尺寸: medium
2025-06-12 10:20:48 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\a5a42ed9eb9c20441c943de61ba45526_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\a5a42ed9eb9c20441c943de61ba45526_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\a5a42ed9eb9c20441c943de61ba45526_medium.jpg
2025-06-12 10:20:48 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 36
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件ID: 36, 文件信息: {'id': 36, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (15) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (15) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (15) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (15) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (15) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:48 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (15) - 副本.png, 尺寸: medium
2025-06-12 10:20:48 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\557cddb0399d9ab9d23b8f56bfb11c7c_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\557cddb0399d9ab9d23b8f56bfb11c7c_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\557cddb0399d9ab9d23b8f56bfb11c7c_medium.jpg
2025-06-12 10:20:48 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 37
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件ID: 37, 文件信息: {'id': 37, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (15).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (15).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (15).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (15).png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (15).png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:48 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (15).png, 尺寸: medium
2025-06-12 10:20:48 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\1b863488ef8143e135dccce614d01e9f_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\1b863488ef8143e135dccce614d01e9f_medium.jpg
2025-06-12 10:20:48 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\1b863488ef8143e135dccce614d01e9f_medium.jpg
2025-06-12 10:20:48 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 38
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件ID: 38, 文件信息: {'id': 38, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (16) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (16) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (16) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (16) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:48 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (16) - 副本.png
2025-06-12 10:20:48 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:48 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (16) - 副本.png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\23b12ea65ad85aff6ffa4a0ea17dcf18_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\23b12ea65ad85aff6ffa4a0ea17dcf18_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\23b12ea65ad85aff6ffa4a0ea17dcf18_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 39
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 39, 文件信息: {'id': 39, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (16).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (16).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (16).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (16).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (16).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (16).png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\008332f84ad614c5ff2fb8771fa1552d_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\008332f84ad614c5ff2fb8771fa1552d_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\008332f84ad614c5ff2fb8771fa1552d_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 40
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 40, 文件信息: {'id': 40, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (17) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (17) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (17) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (17) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (17) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (17) - 副本.png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\140c4eb1c65a069c17256390c75e598d_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\140c4eb1c65a069c17256390c75e598d_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\140c4eb1c65a069c17256390c75e598d_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 41
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 41, 文件信息: {'id': 41, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (17).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (17).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (17).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (17).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (17).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (17).png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\b07043a7d6302cfbe16251c139de992b_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\b07043a7d6302cfbe16251c139de992b_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\b07043a7d6302cfbe16251c139de992b_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 42
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 42, 文件信息: {'id': 42, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (18) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (18) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (18) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (18) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (18) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (18) - 副本.png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\80957270f72746bab44854bd8f85f659_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\80957270f72746bab44854bd8f85f659_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\80957270f72746bab44854bd8f85f659_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 43
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 43, 文件信息: {'id': 43, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (18).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (18).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (18).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (18).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (18).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (18).png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\1e371116749a93d1ead1c591fdbc76d8_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\1e371116749a93d1ead1c591fdbc76d8_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\1e371116749a93d1ead1c591fdbc76d8_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 44
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 44, 文件信息: {'id': 44, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (19) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (19) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (19) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (19) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (19) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (19) - 副本.png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\0069f1d55932b7e76fd0085d55ccb8da_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0069f1d55932b7e76fd0085d55ccb8da_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0069f1d55932b7e76fd0085d55ccb8da_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 45
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 45, 文件信息: {'id': 45, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (19).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (19).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (19).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (19).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (19).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (19).png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\611b949c6e16d94f750b699199ec8a72_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\611b949c6e16d94f750b699199ec8a72_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\611b949c6e16d94f750b699199ec8a72_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 46
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 46, 文件信息: {'id': 46, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (2) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (2) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (2) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (2) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (2) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (2) - 副本.png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\660a9360dca0cc6e14b523df1f6dd964_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\660a9360dca0cc6e14b523df1f6dd964_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\660a9360dca0cc6e14b523df1f6dd964_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 47
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 47, 文件信息: {'id': 47, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (2).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (2).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (2).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (2).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (2).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (2).png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\7c69dc49cfe7ee4838258bb68d996f4f_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7c69dc49cfe7ee4838258bb68d996f4f_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7c69dc49cfe7ee4838258bb68d996f4f_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 48
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 48, 文件信息: {'id': 48, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (20) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (20) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (20) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (20) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (20) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (20) - 副本.png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\781d464c3591b111a866a34105ee92ac_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\781d464c3591b111a866a34105ee92ac_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\781d464c3591b111a866a34105ee92ac_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 49
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 49, 文件信息: {'id': 49, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (20).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (20).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (20).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (20).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (20).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (20).png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\5a7063140b78adec405f9af9ec8118ff_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\5a7063140b78adec405f9af9ec8118ff_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\5a7063140b78adec405f9af9ec8118ff_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 50
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 50, 文件信息: {'id': 50, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (21) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (21) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (21) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (21) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (21) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (21) - 副本.png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\105c2724b67d27a636655e80d48872f1_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\105c2724b67d27a636655e80d48872f1_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\105c2724b67d27a636655e80d48872f1_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 51
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 51, 文件信息: {'id': 51, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (21).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (21).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (21).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (21).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (21).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (21).png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\7def478bdda308daee0fef992f6459c0_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7def478bdda308daee0fef992f6459c0_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7def478bdda308daee0fef992f6459c0_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 52
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 52, 文件信息: {'id': 52, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (22) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (22) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (22) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (22) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (22) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (22) - 副本.png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\481b5b3c134f8d08e30ccf16cb8adb80_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\481b5b3c134f8d08e30ccf16cb8adb80_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\481b5b3c134f8d08e30ccf16cb8adb80_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 53
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 53, 文件信息: {'id': 53, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (22).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (22).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (22).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (22).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (22).png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (22).png, 尺寸: medium
2025-06-12 10:20:49 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\3cd98897ec79cd7aea196f730dd400f5_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3cd98897ec79cd7aea196f730dd400f5_medium.jpg
2025-06-12 10:20:49 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3cd98897ec79cd7aea196f730dd400f5_medium.jpg
2025-06-12 10:20:49 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 54
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件ID: 54, 文件信息: {'id': 54, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (23) - 副本.png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (23) - 副本.png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (23) - 副本.png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (23) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:49 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (23) - 副本.png
2025-06-12 10:20:49 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:49 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (23) - 副本.png, 尺寸: medium
2025-06-12 10:20:50 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\fd2c4dcc3ac2884ca4ede70c52581c1c_medium.jpg
2025-06-12 10:20:50 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\fd2c4dcc3ac2884ca4ede70c52581c1c_medium.jpg
2025-06-12 10:20:50 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\fd2c4dcc3ac2884ca4ede70c52581c1c_medium.jpg
2025-06-12 10:20:50 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 55
2025-06-12 10:20:50 - APIServer - INFO - 缩略图请求 - 文件ID: 55, 文件信息: {'id': 55, 'folder_id': 4, 'filename': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (23).png', 'relative_path': '1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (23).png', 'file_size': 1069399, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-04T09:14:46.171949', 'created_at': '2025-06-12T02:20:38', 'last_accessed': None}, 'full_path': 'C:\\设计图纸\\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (23).png', 'current_size': 1069399, 'current_modified': '2025-06-04T09:14:46.171949', 'exists': True}
2025-06-12 10:20:50 - APIServer - INFO - 缩略图请求 - 文件名: 1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (23).png
2025-06-12 10:20:50 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-12 10:20:50 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-12 10:20:50 - APIServer - INFO - 缩略图请求 - 完整路径: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (23).png
2025-06-12 10:20:50 - APIServer - INFO - 缩略图服务可用: True
2025-06-12 10:20:50 - APIServer - INFO - 开始生成缩略图: C:\设计图纸\1c6650dd-fe1b-4af8-9d1b-c619d37e745f - 副本 (23).png, 尺寸: medium
2025-06-12 10:20:50 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\d33170fbe1f943832b3a793657474e83_medium.jpg
2025-06-12 10:20:50 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d33170fbe1f943832b3a793657474e83_medium.jpg
2025-06-12 10:20:50 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d33170fbe1f943832b3a793657474e83_medium.jpg
2025-06-12 10:22:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:22:04 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 316, 'total_size': 344487844, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T02:20:38', 'last_scanned': '2025-06-12T10:20:38.659662', 'file_count': 316}]}
2025-06-12 10:22:04 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:22:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:22:04 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 316, 'total_size': 344487844, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T02:20:38', 'last_scanned': '2025-06-12T10:20:38.659662', 'file_count': 316}]}
2025-06-12 10:22:04 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:22:05 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:22:05 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 316, 'total_size': 344487844, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T02:20:38', 'last_scanned': '2025-06-12T10:20:38.659662', 'file_count': 316}]}
2025-06-12 10:22:05 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:22:05 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:22:05 - APIServer - INFO - 成功获取 4 条下载记录
2025-06-12 10:22:05 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:22:05 - APIServer - INFO - 成功获取 4 条下载记录
2025-06-12 10:22:05 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:22:05 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 316, 'total_size': 344487844, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T02:20:38', 'last_scanned': '2025-06-12T10:20:38.659662', 'file_count': 316}]}
2025-06-12 10:22:05 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:25:41 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 10:28:02 - APIServer - INFO - API服务器已停止
2025-06-12 10:28:03 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 10:28:03 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-12 10:28:23 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-12 10:29:01 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:29:01 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 316, 'total_size': 344487844, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T02:20:38', 'last_scanned': '2025-06-12T10:20:38.659662', 'file_count': 316}]}
2025-06-12 10:29:01 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:29:01 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-12 10:29:01 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 4, 'name': '设计图纸', 'path': 'C:\\设计图纸', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 316, 'total_size': 344487844, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-12T01:36:37', 'updated_at': '2025-06-12T02:20:38', 'last_scanned': '2025-06-12T10:20:38.659662', 'file_count': 316}]}
2025-06-12 10:29:01 - APIServer - INFO - 返回 1 个文件夹
2025-06-12 10:30:00 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:30:00 - APIServer - INFO - 成功获取 5 条下载记录
2025-06-12 10:30:00 - APIServer - INFO - 获取用户 2 的下载记录，页码: 1, 限制: 50
2025-06-12 10:30:00 - APIServer - INFO - 成功获取 5 条下载记录
