2025-06-12 00:01:54 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-12 00:02:12 - DownloadService - INFO - 记录下载成功: 文件夹ID=2, 用户ID=None, 类型=folder, 加密=False
2025-06-12 00:04:41 - DownloadService - INFO - 记录下载成功: 文件夹ID=2, 用户ID=None, 类型=folder, 加密=False
2025-06-12 00:04:43 - DownloadService - INFO - 记录下载成功: 文件夹ID=2, 用户ID=None, 类型=folder, 加密=False
2025-06-12 00:04:44 - DownloadService - INFO - 记录下载成功: 文件夹ID=2, 用户ID=None, 类型=folder, 加密=False
2025-06-12 00:13:40 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-12 00:13:43 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-12 00:14:03 - DownloadService - INFO - 记录下载成功: 文件夹ID=2, 用户ID=None, 类型=folder, 加密=False
2025-06-12 00:19:44 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-12 00:19:53 - DownloadService - INFO - 记录下载成功: 文件夹ID=2, 用户ID=None, 类型=folder, 加密=False
2025-06-12 00:40:33 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-12 00:40:41 - DownloadService - ERROR - 获取用户下载记录失败: Attribute name 'metadata' is reserved when using the Declarative API.
2025-06-12 00:40:41 - DownloadService - ERROR - 获取用户下载记录失败: Attribute name 'metadata' is reserved when using the Declarative API.
2025-06-12 00:46:37 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-12 00:46:46 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-12 00:47:44 - DownloadService - INFO - 创建下载批次成功: 442174ba-f8c9-48a3-a4dc-481b21e41f47, 类型=folder, 目标=folder:2
2025-06-12 00:47:45 - DownloadService - INFO - 批次状态更新成功: 442174ba-f8c9-48a3-a4dc-481b21e41f47 -> ready
2025-06-12 00:47:45 - DownloadService - ERROR - 更新用户活动统计失败: unsupported operand type(s) for +=: 'NoneType' and 'int'
2025-06-12 00:47:45 - DownloadService - INFO - 记录下载成功: 文件夹ID=2, 用户ID=None, 类型=folder, 批次=442174ba-f8c9-48a3-a4dc-481b21e41f47, 加密=False
2025-06-12 00:50:29 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-12 00:50:37 - DownloadService - INFO - 创建下载批次成功: e097e215-774c-4bcc-8635-fec919622c58, 类型=folder, 目标=folder:2
2025-06-12 00:50:37 - DownloadService - INFO - 批次状态更新成功: e097e215-774c-4bcc-8635-fec919622c58 -> ready
2025-06-12 00:50:37 - DownloadService - INFO - 记录下载成功: 文件夹ID=2, 用户ID=None, 类型=folder, 批次=e097e215-774c-4bcc-8635-fec919622c58, 加密=False
2025-06-12 00:50:44 - DownloadService - INFO - 创建下载批次成功: e2ed7c8f-2647-40f8-8563-61e7e7f9e47c, 类型=folder, 目标=folder:2
2025-06-12 00:50:44 - DownloadService - INFO - 批次状态更新成功: e2ed7c8f-2647-40f8-8563-61e7e7f9e47c -> ready
2025-06-12 00:50:44 - DownloadService - INFO - 记录下载成功: 文件夹ID=2, 用户ID=None, 类型=folder, 批次=e2ed7c8f-2647-40f8-8563-61e7e7f9e47c, 加密=False
2025-06-12 08:44:39 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-12 09:33:09 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-12 09:33:59 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-12 09:37:01 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such table: download_batches
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:37:01 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such table: download_batches
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:44:18 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-12 09:46:29 - DownloadService - INFO - 下载服务初始化完成 - 加密阈值: 3, 密码申请限制: 5
2025-06-12 09:46:40 - DownloadService - INFO - 创建下载批次成功: edfd7803-f4ee-4fe3-90c9-d4549f11218e, 类型=folder, 目标=folder:4
2025-06-12 09:46:40 - DownloadService - INFO - 批次状态更新成功: edfd7803-f4ee-4fe3-90c9-d4549f11218e -> ready
2025-06-12 09:46:40 - DownloadService - ERROR - 更新用户活动统计失败: unsupported operand type(s) for +=: 'NoneType' and 'int'
2025-06-12 09:46:40 - DownloadService - ERROR - 记录下载失败: (sqlite3.OperationalError) table download_records has no column named batch_id
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, batch_id, session_id, ip_address, user_agent, download_source, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?) RETURNING id, created_at]
[parameters: (None, 4, None, 1, None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.2.384', 'web', 'folder', 'folder_设计图纸_20250612_094640.zip', 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_设计图纸_20250612_094640.zip', 2161690, 0, None, None, 'completed', None, '2025-06-12 09:46:40.535743')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:47:34 - DownloadService - INFO - 创建下载批次成功: 6844dda0-0333-4b0b-af86-bd19b7301fd1, 类型=folder, 目标=folder:4
2025-06-12 09:47:34 - DownloadService - INFO - 批次状态更新成功: 6844dda0-0333-4b0b-af86-bd19b7301fd1 -> ready
2025-06-12 09:47:34 - DownloadService - ERROR - 更新用户活动统计失败: unsupported operand type(s) for +=: 'NoneType' and 'int'
2025-06-12 09:47:34 - DownloadService - ERROR - 记录下载失败: (sqlite3.OperationalError) table download_records has no column named batch_id
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, batch_id, session_id, ip_address, user_agent, download_source, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?) RETURNING id, created_at]
[parameters: (None, 4, None, 2, None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.2.384', 'web', 'folder', 'folder_设计图纸_20250612_094734.zip', 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_设计图纸_20250612_094734.zip', 2161690, 0, None, None, 'completed', None, '2025-06-12 09:47:34.197007')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:47:37 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:47:37 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:49:20 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:49:20 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:55:34 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:55:34 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:56:05 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:56:05 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:56:05 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:56:05 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
