@echo off
title File Share System - SQLite

echo.
echo ========================================
echo   File Share System - SQLite Version
echo ========================================
echo.

:: Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found, please install Python 3.7+
    echo.
    echo Download: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python environment check passed

:: Check if in correct directory
if not exist "backend\main.py" (
    echo Error: Please run this script from project root directory
    pause
    exit /b 1
)

:: Install dependencies
echo.
echo Installing dependencies...
pip install -r backend\requirements.txt --quiet

if errorlevel 1 (
    echo Warning: Some dependencies failed to install, but system may still work
)

:: Check if database exists
if not exist "backend\data\file_share_system.db" (
    echo.
    echo First run, initializing SQLite database...
    cd backend
    python init_database_sqlite.py
    cd ..

    if errorlevel 1 (
        echo Database initialization failed
        pause
        exit /b 1
    )

    echo Database initialization successful
) else (
    echo Database already exists
)

:: Start server
echo.
echo Starting File Share System...
echo.
echo Default admin account:
echo   Username: admin
echo   Password: admin123
echo.
echo System will auto-open browser, if not, please visit:
echo   Frontend: http://localhost:8082
echo   API: http://localhost:8086
echo.
echo Press Ctrl+C to stop server
echo.

cd backend
python main.py

:: If program exits with error, show error info
if errorlevel 1 (
    echo.
    echo Server startup failed, please check error messages
    echo.
    echo Common solutions:
    echo 1. Check if ports 8082 and 8086 are occupied
    echo 2. Check if Python dependencies are installed
    echo 3. Check if database file is normal
    echo.
    pause
)

cd ..
